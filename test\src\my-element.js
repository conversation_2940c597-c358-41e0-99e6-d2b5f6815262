import { LitElement, css, html } from 'lit'
import litLogo from './assets/lit.svg'
import viteLogo from '/vite.svg'

export class MyElement extends LitElement {
  constructor() {
    super()
  }

 render() {
    return html`
       <title>tracker</title>

    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css"
    integrity="sha512-xodZBNTC5n17Xt2atTPuE1HxjVMSvLVW9ocqUKLsCC5CXdbqCmblAshOMAS6/keqq/sMZMZ19scR4PsZChSR7A=="
    crossorigin=""/>

    <script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js"
   integrity="sha512-XQoYMqMTK8LvdxXYG3nZ448hOEQiglfqkJs1NOQV44cWnUrBc8PkAOcXy20w0vlaXaVUearIOBhiXZ5V3ynxwA=="
   crossorigin=""></script>
   <div class="nav-container">
      
    <nav>
      <img id="logo" src="public/waterlogo.png" alt="logo">
      <ul>
        
        <li><a href="/">map</a></li>
        <li><a href="routerepeat">route repeat</a></li>
        <li><a href="addGuest">Create link</a></li>
        <li><a href="addBoat">Add boat</a></li>
      </ul>
      </nav>
    </div>
    <div id="map"></div>
     <script src="public/index.js"></script>
    `
  }
}